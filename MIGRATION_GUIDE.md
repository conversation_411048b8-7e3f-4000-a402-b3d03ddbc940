# Python to TypeScript NestJS Migration Guide
## LangGraph Research Agent Backend Migration

### Executive Summary

This document provides a comprehensive guide for migrating the Python LangGraph research agent backend to TypeScript NestJS while maintaining 100% functional parity. The migration leverages the latest `@google/genai` SDK and NestJS best practices to create a scalable, maintainable, and type-safe backend.

**Migration Scope:**
- Python FastAPI + LangGraph → TypeScript NestJS + LangGraph.js
- Google GenAI Python SDK → `@google/genai` TypeScript SDK
- Pydantic models → Class-validator DTOs
- Python state management → TypeScript state annotations

---

## Current Python Architecture Analysis

### Architecture Overview

The current Python backend implements a sophisticated research agent using LangGraph for workflow orchestration. The system processes user queries through a multi-stage pipeline involving query generation, parallel web research, reflection, and answer finalization.

### Core Components

1. **LangGraph State Machine** (`graph.py`)
   - Multi-node workflow with conditional routing
   - Parallel execution capabilities
   - State persistence across workflow steps

2. **State Management** (`state.py`)
   - TypedDict-based state definitions
   - Operator-based state accumulation
   - Multiple state types for workflow stages

3. **Configuration System** (`configuration.py`)
   - Pydantic-based configuration
   - Environment variable integration
   - Model selection parameters

4. **FastAPI Integration** (`app.py`)
   - Static file serving for React frontend
   - LangGraph API integration

### Current Architecture Diagram

```mermaid
graph TB
    subgraph "Python Backend"
        A[FastAPI App] --> B[LangGraph API]
        B --> C[Graph Service]
        C --> D[Generate Query Node]
        C --> E[Web Research Node]
        C --> F[Reflection Node]
        C --> G[Finalize Answer Node]
        
        D --> H[Google GenAI Client]
        E --> H
        F --> H
        G --> H
        
        C --> I[State Management]
        I --> J[OverallState]
        I --> K[ReflectionState]
        I --> L[QueryGenerationState]
        
        M[Configuration] --> C
        N[Utils] --> E
        N --> G
    end
    
    subgraph "External Services"
        H --> O[Google Gemini API]
        E --> P[Google Search API]
    end
    
    subgraph "Frontend"
        Q[React App] --> A
    end
```

---

## Target NestJS Architecture Design

### Architecture Principles

1. **Modular Design**: Clear separation of concerns with dedicated modules
2. **Dependency Injection**: Leveraging NestJS DI container for loose coupling
3. **Type Safety**: Full TypeScript integration with strict typing
4. **Scalability**: Designed for horizontal scaling and microservices
5. **Maintainability**: Clean code principles and SOLID design patterns

### Module Structure

```
src/
├── app.module.ts                 # Root application module
├── main.ts                       # Application bootstrap
├── config/                       # Configuration management
├── agent/                        # Core agent functionality
├── genai/                        # Google GenAI integration
├── utils/                        # Utility services
├── frontend/                     # Frontend serving
└── common/                       # Shared components
```

### Target Architecture Diagram

```mermaid
graph TB
    subgraph "NestJS Backend"
        A[App Module] --> B[Agent Module]
        A --> C[GenAI Module]
        A --> D[Utils Module]
        A --> E[Frontend Module]
        A --> F[Config Module]
        
        B --> G[Agent Controller]
        B --> H[Agent Service]
        B --> I[Graph Service]
        
        I --> J[Query Generator Node]
        I --> K[Web Research Node]
        I --> L[Reflection Node]
        I --> M[Finalizer Node]
        
        C --> N[GenAI Service]
        C --> O[Gemini Provider]
        C --> P[Search Provider]
        
        D --> Q[Citation Service]
        D --> R[URL Resolver Service]
        D --> S[Text Processor Service]
        
        subgraph "State Management"
            T[State Types]
            U[State Annotations]
            V[LangGraph State]
        end
        
        I --> T
        I --> U
        I --> V
    end
    
    subgraph "External Services"
        N --> W[Google GenAI API]
        P --> X[Google Search API]
    end
    
    subgraph "Frontend"
        Y[React App] --> G
    end
```

---

## Dependency Mapping

### Core Framework Migration

| Python Package | TypeScript/NestJS Equivalent | Migration Notes |
|---|---|---|
| `fastapi` | `@nestjs/core`, `@nestjs/common` | More structured architecture with decorators |
| `langgraph>=0.2.6` | `@langchain/langgraph` | Direct TypeScript equivalent |
| `langchain>=0.3.19` | `@langchain/core` | Core functionality maintained |
| `google-genai` | `@google/genai` | Same package, TypeScript native |
| `pydantic` | `class-validator`, `class-transformer` | NestJS standard validation |
| `python-dotenv` | `@nestjs/config` | NestJS configuration module |

### Required NestJS Dependencies

```json
{
  "dependencies": {
    "@nestjs/core": "^10.0.0",
    "@nestjs/common": "^10.0.0",
    "@nestjs/platform-express": "^10.0.0",
    "@nestjs/config": "^3.0.0",
    "@nestjs/serve-static": "^4.0.0",
    "@nestjs/swagger": "^7.0.0",
    "@nestjs/throttler": "^5.0.0",
    "@langchain/langgraph": "^0.2.0",
    "@langchain/core": "^0.3.0",
    "@google/genai": "^0.8.0",
    "class-validator": "^0.14.0",
    "class-transformer": "^0.5.0",
    "rxjs": "^7.8.0",
    "reflect-metadata": "^0.1.13"
  },
  "devDependencies": {
    "@nestjs/testing": "^10.0.0",
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "jest": "^29.0.0",
    "eslint": "^8.0.0",
    "prettier": "^3.0.0"
  }
}
```

---

## Step-by-Step Migration Plan

### Phase 1: Project Setup and Infrastructure

#### Step 1.1: Initialize NestJS Project
```bash
npm i -g @nestjs/cli
nest new research-agent-backend
cd research-agent-backend
```

#### Step 1.2: Install Dependencies
```bash
npm install @nestjs/config @nestjs/serve-static @nestjs/swagger @nestjs/throttler
npm install @langchain/langgraph @langchain/core @google/genai
npm install class-validator class-transformer
npm install --save-dev @nestjs/testing jest
```

#### Step 1.3: Configure TypeScript
```json
{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "ES2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

### Phase 2: Configuration Module Migration

#### Step 2.1: Create Configuration Schema
```typescript
// src/config/validation.schema.ts
import { IsString, IsNumber, IsOptional } from 'class-validator';

export class ConfigValidationSchema {
  @IsString()
  GEMINI_API_KEY: string;

  @IsString()
  @IsOptional()
  QUERY_GENERATOR_MODEL?: string = 'gemini-2.0-flash';

  @IsString()
  @IsOptional()
  REFLECTION_MODEL?: string = 'gemini-2.5-flash';

  @IsString()
  @IsOptional()
  ANSWER_MODEL?: string = 'gemini-2.5-pro';

  @IsNumber()
  @IsOptional()
  NUMBER_OF_INITIAL_QUERIES?: number = 3;

  @IsNumber()
  @IsOptional()
  MAX_RESEARCH_LOOPS?: number = 2;
}
```

#### Step 2.2: Create Configuration Service
```typescript
// src/config/app.config.ts
import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  geminiApiKey: process.env.GEMINI_API_KEY,
  queryGeneratorModel: process.env.QUERY_GENERATOR_MODEL || 'gemini-2.0-flash',
  reflectionModel: process.env.REFLECTION_MODEL || 'gemini-2.5-flash',
  answerModel: process.env.ANSWER_MODEL || 'gemini-2.5-pro',
  numberOfInitialQueries: parseInt(process.env.NUMBER_OF_INITIAL_QUERIES) || 3,
  maxResearchLoops: parseInt(process.env.MAX_RESEARCH_LOOPS) || 2,
}));
```

### Phase 3: State Management Migration

#### Step 3.1: Define State Types
```typescript
// src/agent/state/state.types.ts
import { BaseMessage } from '@langchain/core/messages';

export interface OverallState {
  messages: BaseMessage[];
  searchQuery: string[];
  webResearchResult: string[];
  sourcesGathered: SourceInfo[];
  initialSearchQueryCount?: number;
  maxResearchLoops?: number;
  researchLoopCount?: number;
  reasoningModel?: string;
}

export interface ReflectionState {
  isSufficient: boolean;
  knowledgeGap: string;
  followUpQueries: string[];
  researchLoopCount: number;
  numberOfRanQueries: number;
}

export interface QueryGenerationState {
  searchQuery: Query[];
}

export interface WebSearchState {
  searchQuery: string;
  id: string;
}

export interface Query {
  query: string;
  rationale: string;
}

export interface SourceInfo {
  label: string;
  shortUrl: string;
  value: string;
}
```

#### Step 3.2: Create State Annotations
```typescript
// src/agent/state/state.annotations.ts
import { Annotation } from '@langchain/langgraph';
import { BaseMessage } from '@langchain/core/messages';
import { SourceInfo } from './state.types';

export const OverallStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  searchQuery: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  webResearchResult: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  sourcesGathered: Annotation<SourceInfo[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  initialSearchQueryCount: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 3,
  }),
  maxResearchLoops: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 2,
  }),
  researchLoopCount: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 0,
  }),
  reasoningModel: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => 'gemini-2.5-pro',
  }),
});
```

### Phase 4: GenAI Module Migration

#### Step 4.1: Create GenAI Service
```typescript
// src/genai/genai.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI } from '@google/genai';

@Injectable()
export class GenAIService {
  private readonly logger = new Logger(GenAIService.name);
  private readonly client: GoogleGenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('app.geminiApiKey');
    if (!apiKey) {
      throw new Error('GEMINI_API_KEY is not configured');
    }
    this.client = new GoogleGenAI({ apiKey });
  }

  async generateContent(params: {
    model: string;
    contents: string;
    config?: any;
  }) {
    try {
      const response = await this.client.models.generateContent({
        model: params.model,
        contents: params.contents,
        config: params.config,
      });
      return response;
    } catch (error) {
      this.logger.error('Error generating content:', error);
      throw error;
    }
  }

  async generateContentStream(params: {
    model: string;
    contents: string;
    config?: any;
  }) {
    try {
      const response = await this.client.models.generateContentStream({
        model: params.model,
        contents: params.contents,
        config: params.config,
      });
      return response;
    } catch (error) {
      this.logger.error('Error generating content stream:', error);
      throw error;
    }
  }

  getClient(): GoogleGenAI {
    return this.client;
  }
}
```

#### Step 4.2: Create Search Provider
```typescript
// src/genai/providers/search.provider.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI } from '@google/genai';

@Injectable()
export class SearchProvider {
  private readonly logger = new Logger(SearchProvider.name);
  private readonly client: GoogleGenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('app.geminiApiKey');
    this.client = new GoogleGenAI({ apiKey });
  }

  async performWebSearch(params: {
    model: string;
    query: string;
    currentDate: string;
  }) {
    try {
      const prompt = `Conduct targeted Google Searches to gather the most recent, credible information on "${params.query}" and synthesize it into a verifiable text artifact.

Instructions:
- Query should ensure that the most current information is gathered. The current date is ${params.currentDate}.
- Conduct multiple, diverse searches to gather comprehensive information.
- Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
- The output should be a well-written summary or report based on your search findings.
- Only include the information found in the search results, don't make up any information.

Research Topic:
${params.query}`;

      const response = await this.client.models.generateContent({
        model: params.model,
        contents: prompt,
        config: {
          tools: [{ google_search: {} }],
          temperature: 0,
        },
      });

      return response;
    } catch (error) {
      this.logger.error('Error performing web search:', error);
      throw error;
    }
  }
}
```

### Phase 5: Graph Nodes Migration

#### Step 5.1: Query Generator Node
```typescript
// src/agent/graph/nodes/query-generator.node.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GenAIService } from '../../../genai/genai.service';
import { OverallState, QueryGenerationState } from '../../state/state.types';

@Injectable()
export class QueryGeneratorNode {
  private readonly logger = new Logger(QueryGeneratorNode.name);

  constructor(
    private genaiService: GenAIService,
    private configService: ConfigService,
  ) {}

  async execute(state: OverallState): Promise<Partial<QueryGenerationState>> {
    try {
      const queryGeneratorModel = this.configService.get<string>('app.queryGeneratorModel');
      const numberOfQueries = state.initialSearchQueryCount || 3;

      const researchTopic = this.getResearchTopic(state.messages);
      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      const prompt = `Your goal is to generate sophisticated and diverse web search queries. These queries are intended for an advanced automated web research tool capable of analyzing complex results, following links, and synthesizing information.

Instructions:
- Always prefer a single search query, only add another query if the original question requests multiple aspects or elements and one query is not enough.
- Each query should focus on one specific aspect of the original question.
- Don't produce more than ${numberOfQueries} queries.
- Queries should be diverse, if the topic is broad, generate more than 1 query.
- Don't generate multiple similar queries, 1 is enough.
- Query should ensure that the most current information is gathered. The current date is ${currentDate}.

Format:
- Format your response as a JSON object with ALL two of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Context: ${researchTopic}`;

      const response = await this.genaiService.generateContent({
        model: queryGeneratorModel,
        contents: prompt,
      });

      // Parse the structured response
      const result = JSON.parse(response.text);

      return {
        searchQuery: result.query.map((q: string, index: number) => ({
          query: q,
          rationale: result.rationale,
        })),
      };
    } catch (error) {
      this.logger.error('Error in query generator node:', error);
      throw error;
    }
  }

  private getResearchTopic(messages: any[]): string {
    if (messages.length === 1) {
      return messages[0].content;
    }

    let researchTopic = '';
    for (const message of messages) {
      if (message.type === 'human') {
        researchTopic += `User: ${message.content}\n`;
      } else if (message.type === 'ai') {
        researchTopic += `Assistant: ${message.content}\n`;
      }
    }
    return researchTopic;
  }
}
```

### Phase 6: Data Transfer Objects (DTOs)

#### Step 6.1: Research Request DTO
```typescript
// src/agent/dto/research-request.dto.ts
import { IsString, IsNumber, IsOptional, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ResearchRequestDto {
  @ApiProperty({
    description: 'The research question or topic',
    example: 'What are the latest developments in quantum computing?',
  })
  @IsString()
  question: string;

  @ApiPropertyOptional({
    description: 'Number of initial search queries to generate',
    example: 3,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  initialSearchQueryCount?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of research loops',
    example: 2,
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @IsNumber()
  maxResearchLoops?: number;

  @ApiPropertyOptional({
    description: 'Model to use for reasoning tasks',
    example: 'gemini-2.5-pro',
  })
  @IsOptional()
  @IsString()
  reasoningModel?: string;
}
```

#### Step 6.2: Research Response DTO
```typescript
// src/agent/dto/research-response.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class SourceInfoDto {
  @ApiProperty()
  label: string;

  @ApiProperty()
  shortUrl: string;

  @ApiProperty()
  value: string;
}

export class ResearchResponseDto {
  @ApiProperty({
    description: 'The final research answer',
  })
  answer: string;

  @ApiProperty({
    description: 'Sources used in the research',
    type: [SourceInfoDto],
  })
  sources: SourceInfoDto[];

  @ApiProperty({
    description: 'Search queries that were executed',
    type: [String],
  })
  searchQueries: string[];

  @ApiProperty({
    description: 'Number of research loops performed',
  })
  researchLoops: number;
}
```

### Phase 7: Agent Controller and Service

#### Step 7.1: Agent Controller
```typescript
// src/agent/agent.controller.ts
import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { AgentService } from './agent.service';
import { ResearchRequestDto } from './dto/research-request.dto';
import { ResearchResponseDto } from './dto/research-response.dto';

@ApiTags('agent')
@Controller('agent')
@UseGuards(ThrottlerGuard)
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Post('research')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Perform research on a given topic',
    description: 'Executes the LangGraph research workflow to provide comprehensive answers',
  })
  @ApiResponse({
    status: 200,
    description: 'Research completed successfully',
    type: ResearchResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests',
  })
  async performResearch(
    @Body() request: ResearchRequestDto,
  ): Promise<ResearchResponseDto> {
    return this.agentService.performResearch(request);
  }
}
```

### Phase 8: Testing Strategy

#### Step 8.1: Unit Tests
```typescript
// src/agent/agent.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { AgentService } from './agent.service';
import { GraphService } from './graph/graph.service';
import { GenAIService } from '../genai/genai.service';

describe('AgentService', () => {
  let service: AgentService;
  let graphService: GraphService;
  let genaiService: GenAIService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentService,
        {
          provide: GraphService,
          useValue: {
            executeWorkflow: jest.fn(),
          },
        },
        {
          provide: GenAIService,
          useValue: {
            generateContent: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AgentService>(AgentService);
    graphService = module.get<GraphService>(GraphService);
    genaiService = module.get<GenAIService>(GenAIService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('performResearch', () => {
    it('should execute research workflow successfully', async () => {
      const mockRequest = {
        question: 'What is quantum computing?',
        initialSearchQueryCount: 3,
        maxResearchLoops: 2,
      };

      const mockResponse = {
        answer: 'Quantum computing is...',
        sources: [],
        searchQueries: ['quantum computing basics'],
        researchLoops: 1,
      };

      jest.spyOn(graphService, 'executeWorkflow').mockResolvedValue(mockResponse);

      const result = await service.performResearch(mockRequest);

      expect(result).toEqual(mockResponse);
      expect(graphService.executeWorkflow).toHaveBeenCalledWith(mockRequest);
    });
  });
});
```

#### Step 8.2: Integration Tests
```typescript
// test/agent.e2e-spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('AgentController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/agent/research (POST)', () => {
    return request(app.getHttpServer())
      .post('/agent/research')
      .send({
        question: 'What is artificial intelligence?',
        initialSearchQueryCount: 2,
        maxResearchLoops: 1,
      })
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('answer');
        expect(res.body).toHaveProperty('sources');
        expect(res.body).toHaveProperty('searchQueries');
        expect(res.body).toHaveProperty('researchLoops');
      });
  });
});
```

---

## Deployment and Configuration

### Environment Configuration

#### Development Environment (.env.development)
```bash
# Google GenAI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Model Configuration
QUERY_GENERATOR_MODEL=gemini-2.0-flash
REFLECTION_MODEL=gemini-2.5-flash
ANSWER_MODEL=gemini-2.5-pro

# Research Configuration
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=2

# Application Configuration
NODE_ENV=development
PORT=3000
```

#### Production Environment (.env.production)
```bash
# Google GenAI Configuration
GEMINI_API_KEY=${GEMINI_API_KEY}

# Model Configuration
QUERY_GENERATOR_MODEL=gemini-2.0-flash
REFLECTION_MODEL=gemini-2.5-flash
ANSWER_MODEL=gemini-2.5-pro

# Research Configuration
NUMBER_OF_INITIAL_QUERIES=3
MAX_RESEARCH_LOOPS=2

# Application Configuration
NODE_ENV=production
PORT=3000

# Security Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=10
```

### Docker Configuration

#### Dockerfile
```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  research-agent:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./frontend/dist:/app/frontend/dist:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

---

## Troubleshooting Guide

### Common Migration Issues

#### 1. State Management Differences
**Problem**: LangGraph.js state annotations behave differently from Python TypedDict
**Solution**: Use proper reducer functions and default values in annotations

```typescript
// Correct approach
export const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y), // Proper array concatenation
    default: () => [], // Always return new array
  }),
});
```

#### 2. Google GenAI SDK Differences
**Problem**: Method signatures differ between Python and TypeScript SDKs
**Solution**: Use the unified `@google/genai` package with proper error handling

```typescript
// Correct implementation
try {
  const response = await this.client.models.generateContent({
    model: 'gemini-2.0-flash',
    contents: prompt,
    config: {
      tools: [{ google_search: {} }],
      temperature: 0,
    },
  });
  return response;
} catch (error) {
  this.logger.error('GenAI error:', error);
  throw new InternalServerErrorException('Failed to generate content');
}
```

#### 3. Dependency Injection Issues
**Problem**: Circular dependencies or missing providers
**Solution**: Use proper module imports and forwardRef when necessary

```typescript
// Correct module configuration
@Module({
  imports: [
    ConfigModule,
    forwardRef(() => GenAIModule),
  ],
  providers: [AgentService, GraphService],
  exports: [AgentService],
})
export class AgentModule {}
```

#### 4. Type Safety Issues
**Problem**: TypeScript compilation errors with LangGraph types
**Solution**: Use proper type assertions and interface definitions

```typescript
// Correct type handling
interface TypedState extends Record<string, any> {
  messages: BaseMessage[];
  searchQuery: string[];
}

const typedState = state as TypedState;
```

### Performance Optimization

#### 1. Memory Management
- Use streaming responses for large content generation
- Implement proper cleanup in node execution
- Monitor memory usage with heap snapshots

#### 2. Concurrent Processing
- Leverage NestJS async capabilities
- Use Promise.all for parallel operations
- Implement proper timeout handling

#### 3. Caching Strategy
- Cache frequently used prompts
- Implement Redis for session state
- Use HTTP caching headers

### Monitoring and Logging

#### Application Metrics
```typescript
// src/common/interceptors/logging.interceptor.ts
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url } = request;
    const start = Date.now();

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - start;
        this.logger.log(`${method} ${url} - ${duration}ms`);
      }),
      catchError((error) => {
        const duration = Date.now() - start;
        this.logger.error(`${method} ${url} - ${duration}ms - Error: ${error.message}`);
        throw error;
      }),
    );
  }
}
```

---

## Validation and Testing

### Functional Parity Checklist

- [ ] Query generation produces equivalent results
- [ ] Web search integration maintains same functionality
- [ ] Reflection logic produces same decisions
- [ ] Answer finalization maintains quality
- [ ] Citation handling preserves accuracy
- [ ] State management maintains consistency
- [ ] Error handling provides same robustness
- [ ] Configuration system supports all options
- [ ] Frontend integration works seamlessly
- [ ] Performance meets or exceeds Python version

### Testing Strategy

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test module interactions
3. **End-to-End Tests**: Test complete workflows
4. **Performance Tests**: Compare with Python baseline
5. **Load Tests**: Verify scalability improvements

### Migration Validation

1. **Side-by-Side Comparison**: Run both versions with same inputs
2. **Output Verification**: Compare research quality and accuracy
3. **Performance Benchmarking**: Measure response times and resource usage
4. **Error Handling**: Test edge cases and error scenarios
5. **Configuration Testing**: Verify all environment variables work

---

## Conclusion

This migration guide provides a comprehensive roadmap for transitioning from the Python LangGraph research agent to a TypeScript NestJS implementation. The new architecture offers improved type safety, better scalability, and enhanced maintainability while preserving 100% functional parity.

Key benefits of the migration:
- **Type Safety**: Full TypeScript integration eliminates runtime type errors
- **Scalability**: NestJS architecture supports microservices and horizontal scaling
- **Maintainability**: Clear module separation and dependency injection
- **Performance**: Optimized for Node.js runtime characteristics
- **Developer Experience**: Better tooling and IDE support

The migration should be performed incrementally, with thorough testing at each phase to ensure functional parity is maintained throughout the process.
```
